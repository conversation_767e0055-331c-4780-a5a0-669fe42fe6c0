import { NextRequest, NextResponse } from 'next/server';
import { InvincibleOrchestrator } from '@/lib/agents/v2/orchestrator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🚀 Starting Invincible V2 execution:', body);
    
    // Create orchestrator instance
    const orchestrator = new InvincibleOrchestrator();
    
    // Execute the multi-agent workflow
    const result = await orchestrator.execute(body);
    
    console.log('✅ Invincible V2 execution completed');
    
    return NextResponse.json({
      success: true,
      result,
      logs: [] // Will be populated by the orchestrator
    });
    
  } catch (error) {
    console.error('❌ Invincible V2 execution failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      logs: []
    }, { status: 500 });
  }
} 