import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { EnhancedAutonomousSupervisor2025 } from '@/lib/agents/autonomous/EnhancedAutonomousSupervisor2025';

export async function POST(req: NextRequest) {
  let startTime = Date.now();
  
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ 
        error: 'Unauthorized - Authentication required',
        code: 'AUTH_REQUIRED'
      }, { status: 401 });
    }

    // Parse and validate request
    const body = await req.json().catch(() => ({}));
    const { goal, config = {} } = body;
    
    // Input validation
    if (!goal || typeof goal !== 'string' || goal.trim().length < 5) {
      return NextResponse.json({ 
        error: 'Invalid goal - Goal must be at least 5 characters long',
        code: 'INVALID_INPUT'
      }, { status: 400 });
    }

    // Sanitize goal
    const sanitizedGoal = goal.trim().substring(0, 500);

    console.log('🚀 Enhanced Autonomous Supervisor 2025 - Starting execution');
    console.log(`👤 User: ${session.user.email}`);
    console.log(`🎯 Goal: "${sanitizedGoal}"`);
    console.log(`⚙️ Config:`, config);
    
    // Enhanced configuration with 2025 best practices
    const enhancedConfig = {
      maxIterations: Math.min(config.maxIterations || 15, 25), // Safety limit
      maxRetries: Math.min(config.maxRetries || 3, 5), // Safety limit
      timeoutMs: Math.min(config.timeoutMs || 900000, 1800000), // Max 30 minutes
      qualityThreshold: Math.max(Math.min(config.qualityThreshold || 85, 95), 70), // 70-95 range
      enableSelfReflection: config.enableSelfReflection !== false, // Default true
      enableQualityGates: config.enableQualityGates !== false, // Default true
      enableRecursionLimits: config.enableRecursionLimits !== false, // Default true
      verboseLogging: config.verboseLogging !== false // Default true
    };

    console.log('🔧 Enhanced config:', enhancedConfig);
    
    // Initialize Enhanced Autonomous Supervisor 2025
    const supervisor = new EnhancedAutonomousSupervisor2025(enhancedConfig);
    
    // Set up timeout protection (additional safety layer)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Execution timeout after ${enhancedConfig.timeoutMs}ms`));
      }, enhancedConfig.timeoutMs + 30000); // Extra 30s buffer
    });
    
    // Execute with timeout protection
    const result = await Promise.race([
      supervisor.executeAutonomous(sanitizedGoal),
      timeoutPromise
    ]);
    
    const executionTime = Date.now() - startTime;
    
    // Validate result
    if (!result) {
      throw new Error('Supervisor returned null result');
    }
    
    console.log('✅ Autonomous execution completed');
    console.log(`⏱️ Execution time: ${executionTime}ms`);
    console.log(`🎯 Success: ${result.success}`);
    console.log(`📊 Quality score: ${result.qualityScore || 0}`);
    
    // Enhanced insights with 2025 metrics
    const insights = {
      // Core metrics
      capabilities: supervisor.getCapabilities(),
      qualityScore: result.qualityScore || 0,
      executionTime,
      
      // Execution metrics
      totalDecisions: result.insights?.totalDecisions || 0,
      successfulPhases: result.insights?.successfulPhases || 0,
      errorCount: result.insights?.errorCount || 0,
      iterationsCompleted: result.insights?.iterationsCompleted || 0,
      
      // Quality metrics
      qualityChecks: result.insights?.qualityChecks || 0,
      averageQuality: result.insights?.averageQuality || 0,
      finalQuality: result.insights?.finalQuality || result.qualityScore || 0,
      
      // Completion metrics
      completionReason: result.insights?.completionReason || 'unknown',
      agentDecisionsCount: result.agentDecisions?.length || 0,
      
      // Performance metrics
      efficiency: result.insights?.successfulPhases && result.insights?.totalDecisions 
        ? (result.insights.successfulPhases / result.insights.totalDecisions * 100).toFixed(1) + '%'
        : '0%',
      
      // Enhanced 2025 metrics
      hasQualityGates: enhancedConfig.enableQualityGates,
      hasSelfReflection: enhancedConfig.enableSelfReflection,
      hasRecursionLimits: enhancedConfig.enableRecursionLimits,
      configuredThreshold: enhancedConfig.qualityThreshold
    };
    
    // Enhanced response format
    const response = {
      success: true,
      result: result.result || {
        title: 'Content Generation Incomplete',
        content: 'The autonomous supervisor was unable to complete content generation.',
        warning: 'This may be due to quality thresholds not being met or execution limits.'
      },
      insights,
      metadata: {
        version: 'EnhancedAutonomousSupervisor2025',
        executionTime,
        timestamp: new Date().toISOString(),
        user: session.user.email,
        goal: sanitizedGoal,
        config: enhancedConfig
      },
      diagnostics: {
        decisions: result.agentDecisions || [],
        errors: result.errors || [],
        qualityHistory: result.insights?.qualityScores || [],
        phases: result.insights || {}
      }
    };
    
    return NextResponse.json(response, {
      headers: {
        'X-Execution-Time': executionTime.toString(),
        'X-Quality-Score': (result.qualityScore || 0).toString(),
        'X-Agent-Version': 'Enhanced2025'
      }
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    console.error('❌ Enhanced Autonomous Supervisor execution failed:', error);
    
    // Enhanced error categorization
    const errorType = error instanceof Error ? error.name : 'UnknownError';
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    let errorCode = 'EXECUTION_FAILED';
    let statusCode = 500;
    
    // Categorize errors for better debugging
    if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
      errorCode = 'EXECUTION_TIMEOUT';
      statusCode = 408;
    } else if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      errorCode = 'QUOTA_EXCEEDED';
      statusCode = 429;
    } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      errorCode = 'NETWORK_ERROR';
      statusCode = 503;
    } else if (errorMessage.includes('validation') || errorMessage.includes('Invalid')) {
      errorCode = 'VALIDATION_ERROR';
      statusCode = 400;
    }
    
    // Enhanced error response
    const errorResponse = {
      success: false,
      error: errorMessage,
      code: errorCode,
      type: errorType,
      metadata: {
        version: 'EnhancedAutonomousSupervisor2025',
        executionTime,
        timestamp: new Date().toISOString(),
        errorOccurred: true
      },
      diagnostics: {
        stack: error instanceof Error ? error.stack : undefined,
        errorDetails: {
          message: errorMessage,
          type: errorType,
          code: errorCode
        }
      },
      recovery: {
        suggestions: [
          'Check your internet connection',
          'Reduce the complexity of your goal',
          'Try again with a shorter goal description',
          'Check if the service is experiencing high load'
        ],
        retryable: !errorMessage.includes('validation') && !errorMessage.includes('auth')
      }
    };
    
    return NextResponse.json(errorResponse, { 
      status: statusCode,
      headers: {
        'X-Execution-Time': executionTime.toString(),
        'X-Error-Code': errorCode,
        'X-Agent-Version': 'Enhanced2025'
      }
    });
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return autonomous supervisor capabilities and status
    const supervisor = new EnhancedAutonomousSupervisor2025();
    
    return NextResponse.json({
      available: true,
      capabilities: supervisor.getCapabilities(),
      supportedGoals: [
        'Create comprehensive articles with web research',
        'Research and competitive analysis',
        'Content optimization and quality enhancement',
        'Self-improving content generation',
        'Multi-agent orchestrated workflows',
        'Intelligent planning with web access'
      ],
      features: [
        'Web-enabled intelligent planning (eliminates generic fallbacks)',
        'True autonomous supervisor orchestration',
        'Quality-based iterative enhancement',
        'Dynamic agent selection and routing',
        'Real-time quality assessment',
        'Context-aware content generation'
      ]
    });

  } catch (error) {
    console.error('❌ Error getting autonomous status:', error);
    return NextResponse.json(
      { error: 'Failed to get autonomous status' },
      { status: 500 }
    );
  }
} 