/**
 * Invincible v.2 Multi-Agent System Test
 * Test file to verify the system is working correctly
 */

import { createInvincibleV2, DEFAULT_CONFIG } from './index';

export async function testInvincibleV2() {
  console.log('🧪 Testing Invincible v.2 Multi-Agent System...');
  
  try {
    // Create orchestrator with default config
    const orchestrator = createInvincibleV2(DEFAULT_CONFIG);
    
    // Display capabilities
    console.log('🔧 System Capabilities:');
    const capabilities = orchestrator.getCapabilities();
    console.log(JSON.stringify(capabilities, null, 2));
    
    // Test with a simple topic
    console.log('\n📝 Testing content generation...');
    const result = await orchestrator.execute({
      topic: 'How to Build a Sustainable Garden',
      targetAudience: 'homeowners',
      contentLength: 1500,
      tone: 'informative',
      keywords: ['sustainable gardening', 'organic garden', 'eco-friendly']
    });
    
    console.log('\n✅ Test Results:');
    console.log('Success:', result.success);
    console.log('Article Generated:', !!result.article);
    console.log('Quality Score:', result.qualityReport.overallScore);
    console.log('Word Count:', result.article?.wordCount || 0);
    console.log('Execution Time:', result.executionMetrics.totalTime + 'ms');
    
    if (result.success && result.article) {
      console.log('\n📄 Generated Article Sample:');
      console.log('Title:', result.article.title);
      console.log('Meta Description:', result.article.metaDescription);
      console.log('Keywords:', result.article.keywords?.join(', '));
      console.log('Content Preview:', result.article.content?.substring(0, 200) + '...');
    }
    
    if (result.error) {
      console.error('❌ Error:', result.error);
    }
    
    console.log('\n📊 Final Analysis:');
    console.log('Research Summary:', result.researchSummary);
    console.log('Competition Insights:', result.competitionInsights);
    console.log('Quality Report:', result.qualityReport);
    
    return result;
    
  } catch (error) {
    console.error('🚨 Test failed:', error);
    throw error;
  }
}

// Export for external use
export { testInvincibleV2 as runInvincibleV2Test };

// For direct execution
if (require.main === module) {
  testInvincibleV2()
    .then(() => console.log('✅ Test completed successfully'))
    .catch(error => console.error('❌ Test failed:', error));
} 