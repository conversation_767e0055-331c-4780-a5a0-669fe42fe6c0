/**
 * Invincible v.2 Multi-Agent Orchestrator
 * Sequential workflow manager for autonomous content generation
 */

import { ResearchAgent } from './research-agent';
import { CompetitionAgent } from './competition-agent';
import { WritingAgent } from './writing-agent';
import { QualityAgent } from './quality-agent';
import { AgentState, AgentPhase, MultiAgentConfig, MultiAgentResult, WorkflowStep } from './types';

export class InvincibleOrchestrator {
  private researchAgent: ResearchAgent;
  private competitionAgent: CompetitionAgent;
  private writingAgent: WritingAgent;
  private qualityAgent: QualityAgent;
  private config: MultiAgentConfig;
  private workflowSteps: WorkflowStep[] = [];

  constructor(config: Partial<MultiAgentConfig> = {}) {
    this.config = {
      research: {
        searchDepth: 3,
        maxUrls: 10,
        parallelSearches: 3,
        researchQueries: 5,
        ...config.research
      },
      competition: {
        analysisDepth: 'comprehensive',
        includeBacklinks: true,
        includeTechnicalSeo: true,
        includeContentGaps: true,
        ...config.competition
      },
      writing: {
        humanizationLevel: 'maximum',
        creativityLevel: 0.8,
        seoOptimization: true,
        externalLinking: true,
        tableGeneration: true,
        ...config.writing
      },
      quality: {
        aiDetectionCheck: true,
        grammarCheck: true,
        factCheck: true,
        seoValidation: true,
        readabilityCheck: true,
        qualityThreshold: 85,
        ...config.quality
      },
      maxExecutionTime: config.maxExecutionTime || 900000, // 15 minutes
      enableParallelProcessing: config.enableParallelProcessing ?? true,
      enableAdaptiveLearning: config.enableAdaptiveLearning ?? true,
      qualityThreshold: config.qualityThreshold || 85,
      enableHumanLoop: config.enableHumanLoop ?? false
    };

    // Initialize agents
    this.researchAgent = new ResearchAgent(this.config.research);
    this.competitionAgent = new CompetitionAgent(this.config.competition);
    this.writingAgent = new WritingAgent(this.config.writing);
    this.qualityAgent = new QualityAgent(this.config.quality);

  }

  private async runSequentialWorkflow(state: AgentState): Promise<AgentState> {
    try {
      // Execute research phase
      this.log(state, '🔬 Starting research phase');
      state = await this.executeResearchAgent(state);
      if (state.currentPhase === AgentPhase.FAILED) return state;

      // Execute competition analysis phase
      this.log(state, '🏆 Starting competition analysis phase');
      state = await this.executeCompetitionAgent(state);
      if (state.currentPhase === AgentPhase.FAILED) return state;

      // Execute writing phase
      this.log(state, '✍️ Starting writing phase');
      state = await this.executeWritingAgent(state);
      if (state.currentPhase === AgentPhase.FAILED) return state;

      // Execute quality assurance phase
      this.log(state, '🔍 Starting quality assurance phase');
      state = await this.executeQualityAgent(state);
      if (state.currentPhase === AgentPhase.FAILED) return state;

      // Mark workflow as completed
      state.currentPhase = AgentPhase.COMPLETED;
      this.log(state, '✅ Workflow completed successfully');

      return state;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown workflow error';
      state.errors.push({
        phase: 'workflow',
        error: errorMessage,
        timestamp: Date.now()
      });
      state.currentPhase = AgentPhase.FAILED;
      this.log(state, `❌ Workflow failed: ${errorMessage}`);
      return state;
    }
  }

  // Agent execution methods
  private async executeResearchAgent(state: AgentState): Promise<AgentState> {
    const stepId = this.recordWorkflowStep('research', 'running');
    
    try {
      const result = await this.researchAgent.execute(state);
      this.updateWorkflowStep(stepId, 'completed', result);
      return result;
    } catch (error) {
      this.updateWorkflowStep(stepId, 'failed', null, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private async executeCompetitionAgent(state: AgentState): Promise<AgentState> {
    const stepId = this.recordWorkflowStep('competition', 'running');
    
    try {
      const result = await this.competitionAgent.execute(state);
      this.updateWorkflowStep(stepId, 'completed', result);
      return result;
    } catch (error) {
      this.updateWorkflowStep(stepId, 'failed', null, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private async executeWritingAgent(state: AgentState): Promise<AgentState> {
    const stepId = this.recordWorkflowStep('writing', 'running');
    
    try {
      const result = await this.writingAgent.execute(state);
      this.updateWorkflowStep(stepId, 'completed', result);
      return result;
    } catch (error) {
      this.updateWorkflowStep(stepId, 'failed', null, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private async executeQualityAgent(state: AgentState): Promise<AgentState> {
    const stepId = this.recordWorkflowStep('quality', 'running');
    
    try {
      const result = await this.qualityAgent.execute(state);
      this.updateWorkflowStep(stepId, 'completed', result);
      return result;
    } catch (error) {
      this.updateWorkflowStep(stepId, 'failed', null, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  // Validation methods
  private async validateResearch(state: AgentState): Promise<AgentState> {
    this.log(state, '🔍 Validating research phase results');
    
    const hasValidData = state.researchData && state.researchData.length > 0;
    const hasUrls = state.primaryUrls && state.primaryUrls.length > 0;
    
    if (!hasValidData || !hasUrls) {
      this.log(state, '❌ Research validation failed: Insufficient data');
      state.errors.push({
        phase: 'research_validation',
        error: 'Insufficient research data collected',
        timestamp: Date.now()
      });
    }
    
    return state;
  }

  private async validateCompetition(state: AgentState): Promise<AgentState> {
    this.log(state, '🔍 Validating competition analysis results');
    
    const hasAnalysis = state.competitorAnalysis && 
                       state.competitorAnalysis.seoAnalysis &&
                       state.competitorAnalysis.contentGaps;
    
    if (!hasAnalysis) {
      this.log(state, '❌ Competition validation failed: Incomplete analysis');
      state.errors.push({
        phase: 'competition_validation',
        error: 'Incomplete competition analysis',
        timestamp: Date.now()
      });
    }
    
    return state;
  }

  private async validateWriting(state: AgentState): Promise<AgentState> {
    this.log(state, '🔍 Validating writing results');
    
    const hasContent = state.generatedContent && 
                      state.generatedContent.content &&
                      state.generatedContent.content.length > 500;
    
    if (!hasContent) {
      this.log(state, '❌ Writing validation failed: Insufficient content');
      state.errors.push({
        phase: 'writing_validation',
        error: 'Generated content too short or missing',
        timestamp: Date.now()
      });
    }
    
    return state;
  }

  private async validateQuality(state: AgentState): Promise<AgentState> {
    this.log(state, '🔍 Validating quality assurance results');
    
    const hasAssessment = state.qualityAssessment && 
                         state.qualityAssessment.overallScore > 0;
    
    if (!hasAssessment) {
      this.log(state, '❌ Quality validation failed: No assessment');
      state.errors.push({
        phase: 'quality_validation',
        error: 'Quality assessment failed or missing',
        timestamp: Date.now()
      });
    }
    
    return state;
  }

  private async handleFailure(state: AgentState): Promise<AgentState> {
    this.log(state, '💥 Handling workflow failure');
    
    state.currentPhase = AgentPhase.FAILED;
    state.executionTime = Date.now() - state.startTime;
    
    // Log failure summary
    const errorSummary = state.errors.map(e => `${e.phase}: ${e.error}`).join('; ');
    this.log(state, `🚨 Workflow failed: ${errorSummary}`);
    
    return state;
  }

  // Conditional edge methods
  private shouldContinueFromResearch(state: AgentState): string {
    const hasErrors = state.errors.some(e => e.phase.includes('research'));
    const canRetry = state.retryCount < state.maxRetries;
    
    if (hasErrors && canRetry) {
      state.retryCount++;
      return 'retry';
    } else if (hasErrors) {
      return 'fail';
    }
    
    return 'continue';
  }

  private shouldContinueFromCompetition(state: AgentState): string {
    const hasErrors = state.errors.some(e => e.phase.includes('competition'));
    const canRetry = state.retryCount < state.maxRetries;
    
    if (hasErrors && canRetry) {
      state.retryCount++;
      return 'retry';
    } else if (hasErrors) {
      return 'fail';
    }
    
    return 'continue';
  }

  private shouldContinueFromWriting(state: AgentState): string {
    const hasErrors = state.errors.some(e => e.phase.includes('writing'));
    const canRetry = state.retryCount < state.maxRetries;
    
    if (hasErrors && canRetry) {
      state.retryCount++;
      return 'retry';
    } else if (hasErrors) {
      return 'fail';
    }
    
    return 'continue';
  }

  private shouldContinueFromQuality(state: AgentState): string {
    const hasErrors = state.errors.some(e => e.phase.includes('quality'));
    const canRetry = state.retryCount < state.maxRetries;
    const qualityScore = state.qualityAssessment?.overallScore || 0;
    
    if (hasErrors && canRetry) {
      state.retryCount++;
      return 'retry';
    } else if (hasErrors) {
      return 'fail';
    }
    
    // Check if quality enhancement is needed
    if (qualityScore < this.config.qualityThreshold && canRetry) {
      state.retryCount++;
      return 'enhance';
    }
    
    return 'complete';
  }

  // Workflow execution methods
  public async execute(request: {
    topic: string;
    customInstructions?: string;
    targetAudience?: string;
    contentLength?: number;
    tone?: string;
    keywords?: string[];
    contentType?: string;
  }): Promise<MultiAgentResult> {
    const startTime = Date.now();
    const taskId = `inv_v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Initialize state
      const initialState: AgentState = {
        topic: request.topic,
        customInstructions: request.customInstructions,
        targetAudience: request.targetAudience,
        contentLength: request.contentLength || 2000,
        tone: request.tone || 'professional',
        keywords: request.keywords || [],
        contentType: request.contentType || 'article',
        
        currentPhase: AgentPhase.RESEARCH,
        completedPhases: [],
        errors: [],
        logs: [],
        
        taskId,
        startTime,
        retryCount: 0,
        maxRetries: 3
      };

      this.log(initialState, `🚀 Starting Invincible v.2 execution for: "${request.topic}"`);
      
      // Execute workflow
      const finalState = await this.runSequentialWorkflow(initialState);
      
      // Calculate execution time
      const executionTime = Date.now() - startTime;
      finalState.executionTime = executionTime;
      
      // Build result
      const result: MultiAgentResult = {
        success: finalState.currentPhase === AgentPhase.COMPLETED,
        finalState,
        article: finalState.generatedContent,
        qualityReport: this.buildQualityReport(finalState),
        researchSummary: this.buildResearchSummary(finalState),
        competitionInsights: this.buildCompetitionInsights(finalState),
        executionMetrics: this.buildExecutionMetrics(finalState),
        logs: finalState.logs
      };
      
      if (!result.success) {
        result.error = finalState.errors.map((e: any) => e.error).join('; ');
      }
      
      this.log(finalState, `✅ Invincible v.2 execution completed in ${executionTime}ms`);
      
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown execution error';
      
      return {
        success: false,
        finalState: {
          topic: request.topic,
          currentPhase: AgentPhase.FAILED,
          completedPhases: [],
          errors: [{ phase: 'execution', error: errorMessage, timestamp: Date.now() }],
          logs: [],
          taskId,
          startTime,
          retryCount: 0,
          maxRetries: 3
        },
        qualityReport: {
          humanScore: 0,
          seoScore: 0,
          uniquenessScore: 0,
          readabilityScore: 0,
          overallScore: 0
        },
        researchSummary: {
          urlsAnalyzed: 0,
          queriesExecuted: 0,
          dataPoints: 0,
          sourcesUsed: 0
        },
        competitionInsights: {
          competitorsAnalyzed: 0,
          contentGapsFound: 0,
          rankingOpportunities: []
        },
        executionMetrics: {
          totalTime: Date.now() - startTime,
          agentExecutionTimes: {},
          retryCount: 0,
          failurePoints: [errorMessage]
        },
        logs: [],
        error: errorMessage
      };
    }
  }

  // Helper methods
  private buildQualityReport(state: AgentState): any {
    const qa = state.qualityAssessment;
    return {
      humanScore: qa?.aiDetectionResults?.humanLikeScore || 0,
      seoScore: qa?.seoResults?.seoScore || 0,
      uniquenessScore: 85, // Default for now
      readabilityScore: qa?.readabilityResults?.readabilityScore || 0,
      overallScore: qa?.overallScore || 0
    };
  }

  private buildResearchSummary(state: AgentState): any {
    return {
      urlsAnalyzed: state.primaryUrls?.length || 0,
      queriesExecuted: state.researchQueries?.length || 0,
      dataPoints: state.researchData?.length || 0,
      sourcesUsed: state.primaryUrls?.length || 0
    };
  }

  private buildCompetitionInsights(state: AgentState): any {
    const analysis = state.competitorAnalysis;
    return {
      competitorsAnalyzed: state.primaryUrls?.length || 0,
      contentGapsFound: analysis?.contentGaps?.length || 0,
      rankingOpportunities: analysis?.contentGaps || []
    };
  }

  private buildExecutionMetrics(state: AgentState): any {
    const agentTimes: Record<string, number> = {};
    
    // Calculate agent execution times from logs
    const agentIds = ['research-agent', 'competition-agent', 'writing-agent', 'quality-agent'];
    agentIds.forEach(agentId => {
      const agentLogs = state.logs.filter(log => log.agent === agentId);
      if (agentLogs.length >= 2) {
        const startTime = agentLogs[0].timestamp;
        const endTime = agentLogs[agentLogs.length - 1].timestamp;
        agentTimes[agentId] = endTime - startTime;
      }
    });
    
    return {
      totalTime: state.executionTime || 0,
      agentExecutionTimes: agentTimes,
      retryCount: state.retryCount,
      failurePoints: state.errors.map(e => e.error)
    };
  }

  private recordWorkflowStep(agent: string, status: 'pending' | 'running' | 'completed' | 'failed'): string {
    const stepId = `${agent}_${Date.now()}`;
    const step: WorkflowStep = {
      id: stepId,
      agent,
      status,
      startTime: status === 'running' ? Date.now() : undefined
    };
    
    this.workflowSteps.push(step);
    return stepId;
  }

  private updateWorkflowStep(stepId: string, status: 'pending' | 'running' | 'completed' | 'failed', result?: any, error?: string): void {
    const step = this.workflowSteps.find(s => s.id === stepId);
    if (step) {
      step.status = status;
      step.endTime = Date.now();
      step.result = result;
      step.error = error;
    }
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: 'orchestrator',
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[orchestrator] ${message}`);
  }

  // Public utility methods
  public getCapabilities() {
    return {
      name: 'Invincible v.2 Orchestrator',
      description: 'Multi-agent workflow orchestrator for autonomous content generation',
      agents: [
        this.researchAgent.getCapabilities(),
        this.competitionAgent.getCapabilities(),
        this.writingAgent.getCapabilities(),
        this.qualityAgent.getCapabilities()
      ],
      features: [
        'Sequential workflow management',
        'Automatic retry and error handling',
        'Quality-based enhancement loops',
        'Comprehensive validation system',
        'Real-time execution monitoring'
      ]
    };
  }

  public getWorkflowSteps(): WorkflowStep[] {
    return this.workflowSteps;
  }

  public getConfig(): MultiAgentConfig {
    return this.config;
  }
} 