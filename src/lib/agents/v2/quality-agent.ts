/**
 * Quality Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for content quality assurance and AI detection validation
 */

import { GeminiService } from '../../gemini';
import { AgentState, AgentPhase, QualityAgentConfig } from './types';

export class QualityAgent {
  private geminiService: GeminiService;
  private config: QualityAgentConfig;
  private agentId: string;

  constructor(config: Partial<QualityAgentConfig> = {}) {
    this.config = {
      aiDetectionCheck: config.aiDetectionCheck ?? true,
      grammarCheck: config.grammarCheck ?? true,
      factCheck: config.factCheck ?? true,
      seoValidation: config.seoValidation ?? true,
      readabilityCheck: config.readabilityCheck ?? true,
      qualityThreshold: config.qualityThreshold ?? 85
    };
    
    this.agentId = 'quality-agent';
    this.geminiService = new GeminiService();
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '🔍 Quality Agent: Starting comprehensive quality assurance');

    try {
      // Ensure we have generated content to validate
      if (!state.generatedContent) {
        throw new Error('Generated content required for quality assurance');
      }

      // Update state to quality assurance phase
      state.currentPhase = AgentPhase.QUALITY_ASSURANCE;
      
      // Step 1: AI Detection Analysis
      const aiDetectionResults = await this.performAiDetectionCheck(state);
      this.log(state, `🤖 AI Detection: ${aiDetectionResults.humanLikeScore}% human-like`);

      // Step 2: Content Quality Assessment
      const qualityResults = await this.performQualityAssessment(state);
      this.log(state, `📊 Quality Score: ${qualityResults.overallScore}%`);

      // Step 3: SEO Validation
      const seoResults = await this.performSeoValidation(state);
      this.log(state, `🔍 SEO Validation: ${seoResults.seoScore}% optimized`);

      // Step 4: Readability Analysis
      const readabilityResults = await this.performReadabilityCheck(state);
      this.log(state, `📖 Readability: ${readabilityResults.readabilityScore}% accessible`);

      // Step 5: Final Enhancement Recommendations
      const enhancementResults = await this.generateEnhancementRecommendations(state, {
        aiDetection: aiDetectionResults,
        quality: qualityResults,
        seo: seoResults,
        readability: readabilityResults
      });

      // Store quality assessment results
      state.qualityAssessment = {
        aiDetectionResults,
        qualityResults,
        seoResults,
        readabilityResults,
        enhancementResults,
        overallScore: this.calculateOverallScore({
          aiDetection: aiDetectionResults,
          quality: qualityResults,
          seo: seoResults,
          readability: readabilityResults
        }),
        timestamp: Date.now()
      };

      // Apply enhancements if needed
      if (state.qualityAssessment.overallScore < (this.config.qualityThreshold || 85)) {
        this.log(state, '🔧 Applying quality enhancements');
        state.generatedContent = await this.applyQualityEnhancements(state, enhancementResults);
      }

      // Mark quality assurance phase as complete
      state.completedPhases.push(AgentPhase.QUALITY_ASSURANCE);
      state.currentPhase = AgentPhase.COMPLETED;

      const executionTime = Date.now() - startTime;
      this.log(state, `✅ Quality Agent completed in ${executionTime}ms`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown quality assurance error';
      state.errors.push({
        phase: 'quality_assurance',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Quality Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performAiDetectionCheck(state: AgentState): Promise<any> {
    this.log(state, '🔍 Performing AI detection analysis');

    const detectionPrompt = `Analyze this content for AI detection patterns and human-like qualities.

**Content to Analyze:**
${state.generatedContent!.content}

**Analysis Framework:**
1. **Perplexity Analysis:**
   - Sentence complexity variation
   - Unexpected word choices
   - Natural language patterns
   - Conversational elements

2. **Burstiness Analysis:**
   - Sentence length variation
   - Paragraph size diversity
   - Information density fluctuation
   - Emotional variation

3. **Human Authenticity Markers:**
   - Personal voice elements
   - Filler words and qualifiers
   - Parenthetical thoughts
   - Rhetorical questions
   - Self-corrections

4. **AI Detection Signals:**
   - Repetitive patterns
   - Overly formal language
   - Lack of personality
   - Uniform sentence structure

**Scoring:**
- Human-like Score: 0-100 (higher is better)
- AI Detection Risk: Low/Medium/High
- Specific improvement areas

Return detailed analysis with actionable recommendations.`;

    try {
      const response = await this.geminiService.generateContent(
        detectionPrompt,
        { temperature: 0.7, maxOutputTokens: 4096 },
        'AI Detection Analysis'
      );

      return this.parseAiDetectionResults(response.response);
    } catch (error) {
      this.log(state, `⚠️ AI detection check failed: ${error}`);
      return { humanLikeScore: 50, riskLevel: 'medium', improvements: [] };
    }
  }

  private async performQualityAssessment(state: AgentState): Promise<any> {
    this.log(state, '📊 Performing comprehensive quality assessment');

    const qualityPrompt = `Evaluate this content for overall quality across multiple dimensions.

**Content:**
${state.generatedContent!.content}

**Quality Assessment Framework:**
1. **Content Quality (30%):**
   - Accuracy and factual correctness
   - Depth and comprehensiveness
   - Unique insights and value
   - Logical flow and structure

2. **Writing Quality (25%):**
   - Grammar and syntax
   - Clarity and coherence
   - Engaging and compelling prose
   - Professional tone

3. **Authority & Credibility (20%):**
   - Expert positioning
   - Credible sources and references
   - Trust-building elements
   - Thought leadership

4. **User Experience (15%):**
   - Readability and accessibility
   - Actionable insights
   - Practical value
   - Clear takeaways

5. **Technical Quality (10%):**
   - Proper formatting
   - Consistent style
   - Error-free content
   - Technical accuracy

**Scoring:**
- Overall Quality Score: 0-100
- Individual dimension scores
- Specific improvement recommendations

Return detailed quality assessment with scores and actionable feedback.`;

    try {
      const response = await this.geminiService.generateContent(
        qualityPrompt,
        { temperature: 0.6, maxOutputTokens: 4096 },
        'Quality Assessment'
      );

      return this.parseQualityResults(response.response);
    } catch (error) {
      this.log(state, `⚠️ Quality assessment failed: ${error}`);
      return { overallScore: 70, dimensions: {}, improvements: [] };
    }
  }

  private async performSeoValidation(state: AgentState): Promise<any> {
    this.log(state, '🔍 Performing SEO validation');

    const seoPrompt = `Validate this content for SEO optimization and search engine performance.

**Content:**
${state.generatedContent!.content}

**Target Topic:** ${state.topic}
**Target Keywords:** ${state.generatedContent!.keywords?.join(', ') || 'Not specified'}

**SEO Validation Framework:**
1. **Keyword Optimization (25%):**
   - Primary keyword usage and density
   - Secondary keyword integration
   - Long-tail keyword coverage
   - Natural keyword placement

2. **Content Structure (25%):**
   - Title optimization
   - Header hierarchy (H1, H2, H3)
   - Meta description quality
   - URL-friendly structure

3. **Technical SEO (20%):**
   - Content length optimization
   - Internal linking opportunities
   - Schema markup potential
   - Mobile-friendly formatting

4. **User Intent (15%):**
   - Search intent alignment
   - Question answering format
   - Featured snippet optimization
   - Voice search optimization

5. **E-A-T Signals (15%):**
   - Expertise demonstration
   - Authority building
   - Trustworthiness indicators
   - Credibility markers

**Scoring:**
- SEO Score: 0-100
- Individual component scores
- Specific optimization recommendations

Return detailed SEO validation with actionable improvements.`;

    try {
      const response = await this.geminiService.generateContent(
        seoPrompt,
        { temperature: 0.5, maxOutputTokens: 4096 },
        'SEO Validation'
      );

      return this.parseSeoResults(response.response);
    } catch (error) {
      this.log(state, `⚠️ SEO validation failed: ${error}`);
      return { seoScore: 70, components: {}, improvements: [] };
    }
  }

  private async performReadabilityCheck(state: AgentState): Promise<any> {
    this.log(state, '📖 Performing readability analysis');

    const content = state.generatedContent!.content;
    
    // Calculate basic readability metrics
    const words = content.split(/\s+/).length;
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    
    const avgWordsPerSentence = words / sentences;
    const avgSentencesPerParagraph = sentences / paragraphs;
    
    // Simple readability score calculation
    const readabilityScore = Math.min(100, Math.max(0, 
      100 - (avgWordsPerSentence - 15) * 2 - (avgSentencesPerParagraph - 4) * 3
    ));

    const readabilityPrompt = `Analyze this content for readability and accessibility.

**Content:**
${content}

**Readability Metrics:**
- Words: ${words}
- Sentences: ${sentences}
- Paragraphs: ${paragraphs}
- Avg Words/Sentence: ${avgWordsPerSentence.toFixed(1)}
- Avg Sentences/Paragraph: ${avgSentencesPerParagraph.toFixed(1)}

**Analysis Framework:**
1. **Sentence Structure:**
   - Sentence length variation
   - Complexity balance
   - Clear and concise expression

2. **Vocabulary:**
   - Appropriate difficulty level
   - Jargon usage
   - Clarity of terminology

3. **Organization:**
   - Logical flow
   - Clear transitions
   - Proper formatting

4. **Accessibility:**
   - Inclusive language
   - Clear explanations
   - Engaging presentation

Provide detailed readability analysis with specific recommendations for improvement.`;

    try {
      const response = await this.geminiService.generateContent(
        readabilityPrompt,
        { temperature: 0.6, maxOutputTokens: 3072 },
        'Readability Analysis'
      );

      return {
        readabilityScore: Math.round(readabilityScore),
        metrics: {
          words,
          sentences,
          paragraphs,
          avgWordsPerSentence: parseFloat(avgWordsPerSentence.toFixed(1)),
          avgSentencesPerParagraph: parseFloat(avgSentencesPerParagraph.toFixed(1))
        },
        analysis: response.response,
        improvements: this.extractReadabilityImprovements(response.response)
      };
    } catch (error) {
      this.log(state, `⚠️ Readability check failed: ${error}`);
      return { readabilityScore: 70, metrics: {}, improvements: [] };
    }
  }

  private async generateEnhancementRecommendations(state: AgentState, results: any): Promise<any> {
    this.log(state, '💡 Generating enhancement recommendations');

    const recommendationPrompt = `Based on the quality assessment results, generate specific enhancement recommendations.

**Assessment Results:**
- AI Detection Score: ${results.aiDetection.humanLikeScore}%
- Quality Score: ${results.quality.overallScore}%
- SEO Score: ${results.seo.seoScore}%
- Readability Score: ${results.readability.readabilityScore}%

**Current Content:**
${state.generatedContent!.content}

**Enhancement Framework:**
1. **Priority Improvements** (Critical issues that must be addressed)
2. **Optimization Opportunities** (Areas for enhancement)
3. **Advanced Enhancements** (Optional improvements for excellence)

For each recommendation, provide:
- Specific issue or opportunity
- Detailed improvement strategy
- Implementation priority (High/Medium/Low)
- Expected impact on quality score

Return actionable enhancement plan with specific edits and modifications.`;

    try {
      const response = await this.geminiService.generateContent(
        recommendationPrompt,
        { temperature: 0.7, maxOutputTokens: 4096 },
        'Enhancement Recommendations'
      );

      return this.parseEnhancementResults(response.response);
    } catch (error) {
      this.log(state, `⚠️ Enhancement recommendations failed: ${error}`);
      return { recommendations: [], priorityLevel: 'low' };
    }
  }

  private async applyQualityEnhancements(state: AgentState, enhancements: any): Promise<any> {
    this.log(state, '🔧 Applying quality enhancements');

    const enhancementPrompt = `Apply the following quality enhancements to improve the content.

**Current Content:**
${state.generatedContent!.content}

**Enhancement Recommendations:**
${JSON.stringify(enhancements, null, 2)}

**Instructions:**
1. Apply all high-priority enhancements
2. Implement medium-priority improvements where possible
3. Maintain content integrity and natural flow
4. Ensure all SEO and readability improvements are integrated
5. Preserve the human-like writing style

Return the enhanced content with all improvements applied.`;

    try {
      const response = await this.geminiService.generateContent(
        enhancementPrompt,
        { temperature: 0.6, maxOutputTokens: 8192 },
        'Quality Enhancement'
      );

      return this.parseEnhancedContent(response.response);
    } catch (error) {
      this.log(state, `⚠️ Quality enhancement failed: ${error}`);
      return state.generatedContent;
    }
  }

  private calculateOverallScore(results: any): number {
    const weights = {
      aiDetection: 0.3,
      quality: 0.3,
      seo: 0.25,
      readability: 0.15
    };

    const score = 
      (results.aiDetection.humanLikeScore * weights.aiDetection) +
      (results.quality.overallScore * weights.quality) +
      (results.seo.seoScore * weights.seo) +
      (results.readability.readabilityScore * weights.readability);

    return Math.round(score);
  }

  private parseAiDetectionResults(response: string): any {
    try {
      // Extract numerical scores and assessments
      const humanLikeMatch = response.match(/human-like\s*score:?\s*(\d+)/i);
      const riskMatch = response.match(/risk:?\s*(low|medium|high)/i);
      
      return {
        humanLikeScore: humanLikeMatch ? parseInt(humanLikeMatch[1]) : 75,
        riskLevel: riskMatch ? riskMatch[1].toLowerCase() : 'medium',
        analysis: response,
        improvements: this.extractImprovements(response)
      };
    } catch (error) {
      return { humanLikeScore: 75, riskLevel: 'medium', improvements: [] };
    }
  }

  private parseQualityResults(response: string): any {
    try {
      const scoreMatch = response.match(/overall\s*(?:quality\s*)?score:?\s*(\d+)/i);
      
      return {
        overallScore: scoreMatch ? parseInt(scoreMatch[1]) : 75,
        analysis: response,
        improvements: this.extractImprovements(response)
      };
    } catch (error) {
      return { overallScore: 75, improvements: [] };
    }
  }

  private parseSeoResults(response: string): any {
    try {
      const scoreMatch = response.match(/seo\s*score:?\s*(\d+)/i);
      
      return {
        seoScore: scoreMatch ? parseInt(scoreMatch[1]) : 75,
        analysis: response,
        improvements: this.extractImprovements(response)
      };
    } catch (error) {
      return { seoScore: 75, improvements: [] };
    }
  }

  private extractReadabilityImprovements(response: string): string[] {
    const improvements = [];
    const lines = response.split('\n');
    
    for (const line of lines) {
      if (line.includes('improve') || line.includes('enhance') || line.includes('optimize')) {
        improvements.push(line.trim());
      }
    }
    
    return improvements.slice(0, 5);
  }

  private parseEnhancementResults(response: string): any {
    try {
      return {
        recommendations: this.extractImprovements(response),
        priorityLevel: response.toLowerCase().includes('critical') ? 'high' : 'medium',
        analysis: response
      };
    } catch (error) {
      return { recommendations: [], priorityLevel: 'low' };
    }
  }

  private parseEnhancedContent(response: string): any {
    try {
      // Extract content from markdown blocks if present
      const markdownMatch = response.match(/```(?:markdown)?\n([\s\S]*?)\n```/);
      const content = markdownMatch ? markdownMatch[1] : response;
      
      return {
        ...this.parseGeneratedContent(content),
        enhanced: true,
        enhancementTimestamp: Date.now()
      };
    } catch (error) {
      return { content: response, enhanced: true };
    }
  }

  private parseGeneratedContent(content: string): any {
    return {
      title: this.extractTitle(content),
      content: content,
      metaDescription: this.extractMetaDescription(content),
      keywords: this.extractKeywords(content),
      wordCount: this.countWords(content)
    };
  }

  private extractTitle(content: string): string | null {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1] : null;
  }

  private extractMetaDescription(content: string): string | null {
    const metaMatch = content.match(/\*\*Meta Description:\*\*\s*(.+)/);
    return metaMatch ? metaMatch[1] : null;
  }

  private extractKeywords(content: string): string[] {
    const words = content.toLowerCase().match(/\b[a-zA-Z]{3,}\b/g) || [];
    const frequency: { [key: string]: number } = {};
    
    words.forEach(word => {
      if (!this.isStopWord(word)) {
        frequency[word] = (frequency[word] || 0) + 1;
      }
    });

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private isStopWord(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    return stopWords.includes(word.toLowerCase());
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }

  private extractImprovements(response: string): string[] {
    const improvements = [];
    const lines = response.split('\n');
    
    for (const line of lines) {
      if (line.match(/^-|\*|\d+\./) && line.length > 20) {
        improvements.push(line.trim());
      }
    }
    
    return improvements.slice(0, 10);
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  getCapabilities() {
    return {
      name: 'Quality Agent',
      description: 'Comprehensive quality assurance and content validation',
      inputTypes: ['generatedContent'],
      outputTypes: ['qualityAssessment', 'enhancedContent'],
      dependencies: ['gemini-service'],
      parallel: false
    };
  }

  getMetrics(state: AgentState) {
    const qualityLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: qualityLogs.length > 0 ? Date.now() - qualityLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'quality_assurance').length === 0 ? 100 : 0,
      qualityScore: state.qualityAssessment?.overallScore || 0,
      aiDetectionScore: state.qualityAssessment?.aiDetectionResults?.humanLikeScore || 0,
      seoScore: state.qualityAssessment?.seoResults?.seoScore || 0,
      readabilityScore: state.qualityAssessment?.readabilityResults?.readabilityScore || 0,
      errorCount: state.errors.filter(e => e.phase === 'quality_assurance').length,
      lastExecution: Date.now()
    };
  }
} 