/**
 * Workflow Visualization Component
 * Shows the overall multi-agent workflow progress with visual indicators
 */

'use client';

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  TrendingUp, 
  PenTool, 
  CheckCircle, 
  ArrowRight,
  Circle,
  Zap
} from 'lucide-react';

interface WorkflowVisualizationProps {
  agentStatuses: {
    [key: string]: {
      status: string;
      progress: number;
      message: string;
    };
  };
  currentPhase: string;
  progress: number;
  isExecuting: boolean;
  className?: string;
}

export function WorkflowVisualization({
  agentStatuses,
  currentPhase,
  progress,
  isExecuting,
  className = ''
}: WorkflowVisualizationProps) {
  const phases = [
    { 
      id: 'research', 
      name: 'Research', 
      icon: Search, 
      description: 'Gathering data and analyzing sources' 
    },
    { 
      id: 'competition', 
      name: 'Competition', 
      icon: TrendingUp, 
      description: 'SEO/GEO/AEO analysis and gap identification' 
    },
    { 
      id: 'writing', 
      name: 'Writing', 
      icon: PenTool, 
      description: 'Superior content generation with AI bypass' 
    },
    { 
      id: 'quality', 
      name: 'Quality', 
      icon: CheckCircle, 
      description: 'Quality assurance and enhancement' 
    }
  ];

  const getPhaseStatus = (phaseId: string) => {
    const agent = agentStatuses[phaseId];
    if (!agent) return 'idle';
    return agent.status;
  };

  const getPhaseColor = (phaseId: string) => {
    const status = getPhaseStatus(phaseId);
    switch (status) {
      case 'completed':
        return 'text-green-500 border-green-200 bg-green-50';
      case 'running':
        return 'text-blue-500 border-blue-200 bg-blue-50';
      case 'pending':
        return 'text-yellow-500 border-yellow-200 bg-yellow-50';
      case 'failed':
        return 'text-red-500 border-red-200 bg-red-50';
      default:
        return 'text-muted-foreground border-gray-200 bg-gray-50';
    }
  };

  const getConnectorColor = (index: number) => {
    const currentPhaseIndex = phases.findIndex(p => p.id === currentPhase);
    if (index < currentPhaseIndex) {
      return 'bg-green-500';
    } else if (index === currentPhaseIndex && isExecuting) {
      return 'bg-blue-500 animate-pulse';
    } else {
      return 'bg-gray-300';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-purple-600" />
            Multi-Agent Workflow
          </div>
          <Badge variant="outline" className="flex items-center gap-1">
            <Circle className={`h-2 w-2 fill-current ${isExecuting ? 'text-blue-500 animate-pulse' : 'text-gray-400'}`} />
            {isExecuting ? 'Executing' : 'Ready'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Overall Progress</span>
            <span className="text-muted-foreground">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-3" />
        </div>

        {/* Phase Flow Visualization */}
        <div className="relative">
          <div className="flex items-center justify-between">
            {phases.map((phase, index) => {
              const Icon = phase.icon;
              const status = getPhaseStatus(phase.id);
              const isActive = currentPhase === phase.id;
              
              return (
                <div key={phase.id} className="flex flex-col items-center space-y-2 flex-1">
                  {/* Phase Icon */}
                  <div className={`
                    relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300
                    ${getPhaseColor(phase.id)}
                    ${isActive ? 'scale-110 shadow-lg' : ''}
                  `}>
                    <Icon className="h-5 w-5" />
                    {status === 'running' && (
                      <div className="absolute -inset-1 rounded-full border-2 border-blue-400 animate-ping opacity-75" />
                    )}
                  </div>
                  
                  {/* Phase Name */}
                  <div className="text-center">
                    <div className={`text-xs font-medium ${
                      isActive ? 'text-foreground' : 'text-muted-foreground'
                    }`}>
                      {phase.name}
                    </div>
                    <div className="text-xs text-muted-foreground max-w-20 text-center">
                      {agentStatuses[phase.id]?.message || 'Ready'}
                    </div>
                  </div>
                  
                  {/* Connector Arrow */}
                  {index < phases.length - 1 && (
                    <div className="absolute top-6 left-1/2 w-full flex items-center justify-center pointer-events-none">
                      <div className="flex items-center">
                        <div className={`h-0.5 w-8 ${getConnectorColor(index)} transition-colors duration-300`} />
                        <ArrowRight className={`h-3 w-3 ml-1 ${getConnectorColor(index).includes('green') ? 'text-green-500' : getConnectorColor(index).includes('blue') ? 'text-blue-500' : 'text-gray-300'}`} />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Current Phase Details */}
        {isExecuting && currentPhase !== 'idle' && (
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Current Phase</span>
            </div>
            <div className="text-sm text-muted-foreground">
              {phases.find(p => p.id === currentPhase)?.description || 'Processing...'}
            </div>
            {agentStatuses[currentPhase] && (
              <div className="mt-2">
                <Progress 
                  value={agentStatuses[currentPhase].progress} 
                  className="h-2"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {agentStatuses[currentPhase].message} - {Math.round(agentStatuses[currentPhase].progress)}%
                </div>
              </div>
            )}
          </div>
        )}

        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4 pt-4 border-t">
          {phases.map((phase) => {
            const status = getPhaseStatus(phase.id);
            const agent = agentStatuses[phase.id];
            
            return (
              <div key={phase.id} className="text-center">
                <div className="text-xs text-muted-foreground">{phase.name}</div>
                <div className={`text-sm font-medium ${
                  status === 'completed' ? 'text-green-600' :
                  status === 'running' ? 'text-blue-600' :
                  status === 'failed' ? 'text-red-600' :
                  'text-muted-foreground'
                }`}>
                  {status === 'completed' ? '✓' :
                   status === 'running' ? `${Math.round(agent?.progress || 0)}%` :
                   status === 'failed' ? '✗' :
                   '—'}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
} 