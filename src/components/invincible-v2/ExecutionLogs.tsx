/**
 * Execution Logs Component
 * Displays real-time execution logs and debugging information
 */

'use client';

import React, { useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Terminal, 
  Download, 
  Trash,
  Search,
  TrendingUp,
  PenTool,
  CheckCircle,
  AlertTriangle,
  Info,
  Clock
} from 'lucide-react';

interface ExecutionLogsProps {
  logs: any[];
  isExecuting: boolean;
  className?: string;
}

interface LogEntry {
  agent: string;
  message: string;
  timestamp: number;
}

export function ExecutionLogs({ logs = [], isExecuting, className = '' }: ExecutionLogsProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [logs]);

  const getAgentIcon = (agentName: string) => {
    switch (agentName) {
      case 'research-agent':
        return <Search className="h-3 w-3" />;
      case 'competition-agent':
        return <TrendingUp className="h-3 w-3" />;
      case 'writing-agent':
        return <PenTool className="h-3 w-3" />;
      case 'quality-agent':
        return <CheckCircle className="h-3 w-3" />;
      case 'orchestrator':
        return <Terminal className="h-3 w-3" />;
      default:
        return <Info className="h-3 w-3" />;
    }
  };

  const getAgentColor = (agentName: string) => {
    switch (agentName) {
      case 'research-agent':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'competition-agent':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'writing-agent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'quality-agent':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'orchestrator':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-muted-foreground bg-muted border-muted';
    }
  };

  const getMessageType = (message: string) => {
    if (message.includes('❌') || message.includes('failed') || message.includes('error')) {
      return 'error';
    }
    if (message.includes('⚠️') || message.includes('warning')) {
      return 'warning';
    }
    if (message.includes('✅') || message.includes('completed') || message.includes('success')) {
      return 'success';
    }
    if (message.includes('🔍') || message.includes('Starting') || message.includes('executing')) {
      return 'info';
    }
    return 'default';
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-muted-foreground bg-muted border-muted';
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const timeStr = date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    const ms = date.getMilliseconds().toString().padStart(3, '0');
    return `${timeStr}.${ms}`;
  };

  const downloadLogs = () => {
    const logContent = logs.map((log: LogEntry) => 
      `[${formatTimestamp(log.timestamp)}] [${log.agent}] ${log.message}`
    ).join('\n');

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `invincible-v2-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    // This would need to be implemented by the parent component
    console.log('Clear logs requested');
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Terminal className="h-5 w-5" />
            Execution Logs
            <Badge variant="outline" className="flex items-center gap-1">
              {isExecuting && <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />}
              {logs.length} entries
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={downloadLogs}
              disabled={logs.length === 0}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearLogs}
              disabled={logs.length === 0}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {logs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Terminal className="h-8 w-8 mx-auto mb-2" />
            <p>No logs yet</p>
            <p className="text-sm">Execute content generation to see logs</p>
          </div>
        ) : (
          <ScrollArea className="h-96" ref={scrollAreaRef}>
            <div className="space-y-2">
              {logs.map((log: LogEntry, index: number) => {
                const messageType = getMessageType(log.message);
                const agentColor = getAgentColor(log.agent);
                const messageColor = getMessageTypeColor(messageType);
                
                return (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-2 rounded-lg bg-muted/20 hover:bg-muted/40 transition-colors"
                  >
                    {/* Timestamp */}
                    <div className="flex items-center gap-1 text-xs text-muted-foreground font-mono min-w-fit">
                      <Clock className="h-3 w-3" />
                      {formatTimestamp(log.timestamp)}
                    </div>
                    
                    {/* Agent Badge */}
                    <Badge 
                      variant="outline" 
                      className={`${agentColor} text-xs min-w-fit`}
                    >
                      {getAgentIcon(log.agent)}
                      <span className="ml-1">
                        {log.agent.replace('-agent', '').replace('orchestrator', 'system')}
                      </span>
                    </Badge>
                    
                    {/* Message */}
                    <div className={`flex-1 text-sm p-2 rounded border ${messageColor}`}>
                      {log.message}
                    </div>
                  </div>
                );
              })}
              
              {/* Real-time indicator */}
              {isExecuting && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground p-2">
                  <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                  Execution in progress...
                </div>
              )}
            </div>
          </ScrollArea>
        )}

        {/* Log Statistics */}
        {logs.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-blue-600">
                  {logs.filter((log: LogEntry) => log.agent === 'research-agent').length}
                </div>
                <div className="text-xs text-muted-foreground">Research</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-600">
                  {logs.filter((log: LogEntry) => log.agent === 'competition-agent').length}
                </div>
                <div className="text-xs text-muted-foreground">Competition</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">
                  {logs.filter((log: LogEntry) => log.agent === 'writing-agent').length}
                </div>
                <div className="text-xs text-muted-foreground">Writing</div>
              </div>
              <div>
                <div className="text-lg font-bold text-orange-600">
                  {logs.filter((log: LogEntry) => log.agent === 'quality-agent').length}
                </div>
                <div className="text-xs text-muted-foreground">Quality</div>
              </div>
            </div>

            {/* Error Summary */}
            {logs.some((log: LogEntry) => getMessageType(log.message) === 'error') && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-800 text-sm font-medium">
                  <AlertTriangle className="h-4 w-4" />
                  {logs.filter((log: LogEntry) => getMessageType(log.message) === 'error').length} Error(s) Detected
                </div>
                <div className="text-xs text-red-600 mt-1">
                  Check the logs above for detailed error information
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 