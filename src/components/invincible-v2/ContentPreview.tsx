/**
 * Content Preview Component
 * Displays generated content with metadata and download options
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  Copy, 
  Eye,
  EyeOff,
  Hash,
  Type,
  FileSearch,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface ContentPreviewProps {
  result: any;
  className?: string;
}

export function ContentPreview({ result, className = '' }: ContentPreviewProps) {
  const [showFullContent, setShowFullContent] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  if (!result || !result.success || !result.article) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Content Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            {result?.error ? (
              <div className="space-y-2">
                <AlertTriangle className="h-8 w-8 mx-auto text-red-500" />
                <p>Content generation failed</p>
                <p className="text-sm">{result.error}</p>
              </div>
            ) : (
              <div className="space-y-2">
                <FileText className="h-8 w-8 mx-auto" />
                <p>Generate content to see preview</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const article = result.article;
  const executionMetrics = result.executionMetrics || {};

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const downloadContent = (format: 'txt' | 'md' | 'html') => {
    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'txt':
        content = `${article.title}\n\n${article.content}`;
        filename = `${article.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'content'}.txt`;
        mimeType = 'text/plain';
        break;
      case 'md':
        content = `# ${article.title}\n\n${article.content}`;
        filename = `${article.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'content'}.md`;
        mimeType = 'text/markdown';
        break;
      case 'html':
        content = `<!DOCTYPE html>
<html>
<head>
  <title>${article.title}</title>
  <meta name="description" content="${article.metaDescription}">
  <meta name="keywords" content="${article.keywords?.join(', ') || ''}">
</head>
<body>
  <h1>${article.title}</h1>
  ${article.content.replace(/\n/g, '<br>')}
</body>
</html>`;
        filename = `${article.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'content'}.html`;
        mimeType = 'text/html';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const previewContent = showFullContent 
    ? article.content 
    : article.content?.substring(0, 500) + (article.content?.length > 500 ? '...' : '');

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generated Content
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {executionMetrics.totalTime ? `${Math.round(executionMetrics.totalTime / 1000)}s` : '—'}
            </Badge>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadContent('txt')}
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="content" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-4">
            {/* Title */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <Type className="h-4 w-4" />
                  Title
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(article.title || '', 'title')}
                >
                  {copiedField === 'title' ? 'Copied!' : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <h2 className="text-lg font-semibold">{article.title}</h2>
              </div>
            </div>

            {/* Meta Description */}
            {article.metaDescription && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium flex items-center gap-2">
                    <FileSearch className="h-4 w-4" />
                    Meta Description
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(article.metaDescription || '', 'meta')}
                  >
                    {copiedField === 'meta' ? 'Copied!' : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">{article.metaDescription}</p>
                </div>
              </div>
            )}

            {/* Content */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Article Content
                  <Badge variant="secondary">{article.wordCount || 0} words</Badge>
                </h3>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFullContent(!showFullContent)}
                  >
                    {showFullContent ? (
                      <>
                        <EyeOff className="h-4 w-4 mr-1" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <Eye className="h-4 w-4 mr-1" />
                        Show All
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(article.content || '', 'content')}
                  >
                    {copiedField === 'content' ? 'Copied!' : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <div className="p-4 bg-muted/50 rounded-lg max-h-96 overflow-y-auto">
                                 <div className="prose prose-sm max-w-none">
                   {previewContent.split('\n').map((paragraph: string, index: number) => (
                     <p key={index} className="mb-2 text-sm leading-relaxed">
                       {paragraph}
                     </p>
                   ))}
                 </div>
              </div>
              {!showFullContent && article.content?.length > 500 && (
                <p className="text-xs text-muted-foreground text-center">
                  Showing first 500 characters. Click "Show All" to see the complete content.
                </p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="space-y-4">
            {/* Keywords */}
            {article.keywords && article.keywords.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Keywords
                </h3>
                <div className="flex flex-wrap gap-2">
                  {article.keywords.map((keyword: string, index: number) => (
                    <Badge key={index} variant="outline">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Content Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="text-lg font-bold text-blue-600">{article.wordCount || 0}</div>
                <div className="text-xs text-muted-foreground">Words</div>
              </div>
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="text-lg font-bold text-purple-600">
                  {Math.ceil((article.wordCount || 0) / 200)}
                </div>
                <div className="text-xs text-muted-foreground">Reading Time (min)</div>
              </div>
            </div>

            {/* Research Sources */}
            {result.researchSummary && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Research Summary</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-muted/50 rounded-lg p-3">
                    <div className="text-lg font-bold text-green-600">
                      {result.researchSummary.urlsAnalyzed || 0}
                    </div>
                    <div className="text-xs text-muted-foreground">URLs Analyzed</div>
                  </div>
                  <div className="bg-muted/50 rounded-lg p-3">
                    <div className="text-lg font-bold text-orange-600">
                      {result.researchSummary.queriesExecuted || 0}
                    </div>
                    <div className="text-xs text-muted-foreground">Research Queries</div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Download Options</h3>
              <div className="grid gap-3">
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => downloadContent('txt')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download as TXT
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => downloadContent('md')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download as Markdown
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => downloadContent('html')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download as HTML
                </Button>
              </div>

              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-2">Copy Individual Fields</h4>
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => copyToClipboard(article.title || '', 'title-copy')}
                  >
                    {copiedField === 'title-copy' ? 'Copied!' : <Copy className="h-4 w-4 mr-2" />}
                    Copy Title
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => copyToClipboard(article.metaDescription || '', 'meta-copy')}
                  >
                    {copiedField === 'meta-copy' ? 'Copied!' : <Copy className="h-4 w-4 mr-2" />}
                    Copy Meta Description
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => copyToClipboard(article.keywords?.join(', ') || '', 'keywords-copy')}
                  >
                    {copiedField === 'keywords-copy' ? 'Copied!' : <Copy className="h-4 w-4 mr-2" />}
                    Copy Keywords
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 