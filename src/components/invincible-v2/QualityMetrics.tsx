/**
 * Quality Metrics Component
 * Displays comprehensive quality assessment results and scores
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Gauge, 
  Shield, 
  Search, 
  BookOpen, 
  Star,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Target
} from 'lucide-react';

interface QualityMetricsProps {
  result: any;
  className?: string;
}

export function QualityMetrics({ result, className = '' }: QualityMetricsProps) {
  if (!result || !result.success) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="h-5 w-5" />
            Quality Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            {result?.error ? (
              <div className="space-y-2">
                <AlertTriangle className="h-8 w-8 mx-auto text-red-500" />
                <p>Quality assessment failed</p>
                <p className="text-sm">{result.error}</p>
              </div>
            ) : (
              <div className="space-y-2">
                <Target className="h-8 w-8 mx-auto" />
                <p>Run content generation to see quality metrics</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const qualityReport = result.qualityReport || {};
  const metrics = [
    {
      name: 'Human Score',
      value: qualityReport.humanScore || 0,
      icon: Shield,
      description: 'AI detection bypass effectiveness',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      name: 'SEO Score',
      value: qualityReport.seoScore || 0,
      icon: Search,
      description: 'Search engine optimization quality',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      name: 'Readability',
      value: qualityReport.readabilityScore || 0,
      icon: BookOpen,
      description: 'Content readability and accessibility',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      name: 'Uniqueness',
      value: qualityReport.uniquenessScore || 0,
      icon: Star,
      description: 'Content originality and uniqueness',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    }
  ];

  const overallScore = qualityReport.overallScore || 0;
  
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return { label: 'Excellent', variant: 'default' as const, className: 'bg-green-100 text-green-800' };
    if (score >= 80) return { label: 'Good', variant: 'secondary' as const, className: 'bg-blue-100 text-blue-800' };
    if (score >= 70) return { label: 'Fair', variant: 'outline' as const, className: 'bg-yellow-100 text-yellow-800' };
    if (score >= 60) return { label: 'Poor', variant: 'outline' as const, className: 'bg-orange-100 text-orange-800' };
    return { label: 'Failed', variant: 'destructive' as const, className: 'bg-red-100 text-red-800' };
  };

  const overallBadge = getScoreBadge(overallScore);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Gauge className="h-5 w-5" />
            Quality Metrics
          </div>
          <Badge className={overallBadge.className}>
            {overallBadge.label}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <span className="text-2xl font-bold">Overall Quality</span>
          </div>
          <div className={`text-4xl font-bold ${getScoreColor(overallScore)}`}>
            {overallScore}%
          </div>
          <Progress value={overallScore} className="h-3 max-w-xs mx-auto" />
        </div>

        {/* Individual Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {metrics.map((metric) => {
            const Icon = metric.icon;
            const badge = getScoreBadge(metric.value);
            
            return (
              <div 
                key={metric.name}
                className={`p-4 rounded-lg border ${metric.borderColor} ${metric.bgColor}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Icon className={`h-4 w-4 ${metric.color}`} />
                    <span className="text-sm font-medium">{metric.name}</span>
                  </div>
                  <Badge variant="outline" className={badge.className}>
                    {metric.value}%
                  </Badge>
                </div>
                <Progress value={metric.value} className="h-2 mb-2" />
                <p className="text-xs text-muted-foreground">
                  {metric.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Quality Insights */}
        {result.qualityAssessment && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Quality Insights
            </h3>
            
            <div className="grid gap-3">
              {/* AI Detection Results */}
              {result.qualityAssessment.aiDetectionResults && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Shield className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">AI Detection Bypass</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Human-like score: {result.qualityAssessment.aiDetectionResults.humanLikeScore}%
                    {result.qualityAssessment.aiDetectionResults.riskLevel && (
                      <span className="ml-2">
                        Risk: {result.qualityAssessment.aiDetectionResults.riskLevel}
                      </span>
                    )}
                  </p>
                </div>
              )}

              {/* SEO Results */}
              {result.qualityAssessment.seoResults && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Search className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">SEO Optimization</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    SEO score: {result.qualityAssessment.seoResults.seoScore}%
                  </p>
                </div>
              )}

              {/* Readability Results */}
              {result.qualityAssessment.readabilityResults && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <BookOpen className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Readability</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Readability score: {result.qualityAssessment.readabilityResults.readabilityScore}%
                    {result.qualityAssessment.readabilityResults.metrics && (
                      <span className="block mt-1">
                        {result.qualityAssessment.readabilityResults.metrics.words} words, 
                        {result.qualityAssessment.readabilityResults.metrics.sentences} sentences
                      </span>
                    )}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Research & Competition Summary */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {result.researchSummary?.urlsAnalyzed || 0}
            </div>
            <div className="text-xs text-muted-foreground">Sources Analyzed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">
              {result.competitionInsights?.contentGapsFound || 0}
            </div>
            <div className="text-xs text-muted-foreground">Content Gaps</div>
          </div>
        </div>

        {/* Success Indicator */}
        {overallScore >= 85 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">High Quality Content Generated</span>
            </div>
            <p className="text-xs text-green-600 mt-1">
              Content meets all quality standards and is ready for publication.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 