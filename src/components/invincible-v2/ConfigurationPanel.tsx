/**
 * Configuration Panel Component
 * Advanced configuration options for the multi-agent system
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ConfigurationPanelProps {
  className?: string;
}

export function ConfigurationPanel({ className = '' }: ConfigurationPanelProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Advanced Configuration</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          Advanced configuration options will be available here.
        </p>
      </CardContent>
    </Card>
  );
} 