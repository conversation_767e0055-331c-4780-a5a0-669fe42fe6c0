/**
 * Agent Status Indicator Component
 * Shows the current status and progress of individual agents
 */

'use client';

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { LucideIcon, CheckCircle, AlertCircle, Clock, Loader } from 'lucide-react';

interface AgentStatusIndicatorProps {
  name: string;
  icon: LucideIcon;
  status: 'idle' | 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  message: string;
  className?: string;
}

export function AgentStatusIndicator({
  name,
  icon: Icon,
  status,
  progress,
  message,
  className = ''
}: AgentStatusIndicatorProps) {
  const getStatusColor = () => {
    switch (status) {
      case 'idle':
        return 'text-muted-foreground';
      case 'pending':
        return 'text-yellow-500';
      case 'running':
        return 'text-blue-500';
      case 'completed':
        return 'text-green-500';
      case 'failed':
        return 'text-red-500';
      default:
        return 'text-muted-foreground';
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'idle':
        return <Badge variant="secondary">Ready</Badge>;
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Waiting</Badge>;
      case 'running':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-200 animate-pulse">
            <Loader className="h-3 w-3 mr-1 animate-spin" />
            Running
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-green-600 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Complete
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="outline" className="text-red-600 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <Loader className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Icon className={`h-4 w-4 ${getStatusColor()}`} />;
    }
  };

  return (
    <Card className={`transition-all duration-200 ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className={`h-4 w-4 ${getStatusColor()}`} />
            {name}
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{message}</span>
            <div className="flex items-center gap-1">
              {getStatusIcon()}
              {status === 'running' && (
                <span className="text-xs text-muted-foreground">{Math.round(progress)}%</span>
              )}
            </div>
          </div>
          
          {(status === 'running' || status === 'completed') && (
            <Progress 
              value={progress} 
              className={`h-2 ${status === 'completed' ? 'text-green-500' : ''}`}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
} 