#!/usr/bin/env node

/**
 * Simple Autonomous Agent Performance Test
 * Tests the optimizations by making API calls to the autonomous endpoint
 */

async function testOptimizedAutonomous() {
  console.log('🚀 Testing Optimized Autonomous Agent via API');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Quick Test',
      goal: 'Benefits of daily exercise',
      expectedMaxTime: 90000, // 1.5 minutes max
    }
  ];

  const results = [];

  for (const testCase of testCases) {
    console.log(`\n🧪 Running ${testCase.name}: "${testCase.goal}"`);
    console.log(`⏱️ Target max time: ${testCase.expectedMaxTime / 1000}s`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch('http://localhost:3000/api/autonomous', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          goal: testCase.goal,
          config: {
            maxIterations: 6,
            maxRetries: 1,
            timeoutMs: 180000,
            qualityThreshold: 75,
            enableSelfReflection: false,
            verboseLogging: false
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const executionTime = Date.now() - startTime;
      
      const testResult = {
        name: testCase.name,
        goal: testCase.goal,
        success: result.success,
        executionTime,
        maxTime: testCase.expectedMaxTime,
        performance: executionTime <= testCase.expectedMaxTime ? 'PASS' : 'SLOW',
        qualityScore: result.qualityScore || 0,
        improvement: `${Math.round((testCase.expectedMaxTime - executionTime) / 1000)}s under target`
      };

      results.push(testResult);

      console.log(`✅ ${testCase.name} completed in ${(executionTime / 1000).toFixed(1)}s`);
      console.log(`📊 Quality: ${testResult.qualityScore}%`);
      console.log(`🎯 Performance: ${testResult.performance}`);
      
      if (testResult.performance === 'PASS') {
        console.log(`🚀 Optimization success: ${testResult.improvement}`);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      results.push({
        name: testCase.name,
        goal: testCase.goal,
        success: false,
        executionTime,
        maxTime: testCase.expectedMaxTime,
        performance: 'FAILED',
        error: error.message,
        qualityScore: 0
      });

      console.log(`❌ ${testCase.name} failed: ${error.message}`);
    }
  }

  // Summary Report
  console.log('\n📊 OPTIMIZATION TEST SUMMARY');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const passCount = results.filter(r => r.performance === 'PASS').length;

  console.log(`✅ Success Rate: ${successCount}/${results.length}`);
  console.log(`🚀 Speed Optimization: ${passCount}/${results.length} passed`);

  if (passCount === results.length && successCount === results.length) {
    console.log('🎉 ALL OPTIMIZATIONS WORKING! Agent is now faster and efficient.');
  } else if (successCount === results.length) {
    console.log('✅ AGENT WORKING! But may need more speed optimization.');
  } else {
    console.log('⚠️ ISSUES DETECTED! Check the error logs above.');
  }

  return results;
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/session');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🔍 Checking if Next.js server is running...');
  
  checkServer()
    .then(isRunning => {
      if (!isRunning) {
        console.log('❌ Next.js server is not running on localhost:3000');
        console.log('💡 Please run "npm run dev" first, then try this test again.');
        process.exit(1);
      }
      
      console.log('✅ Server is running, starting optimization test...');
      return testOptimizedAutonomous();
    })
    .then(results => {
      const allPassed = results.every(r => r.success && r.performance === 'PASS');
      console.log('\n🏁 Optimization test completed!');
      process.exit(allPassed ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testOptimizedAutonomous }; 