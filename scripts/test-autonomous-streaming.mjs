#!/usr/bin/env node

/**
 * Test script for autonomous streaming endpoint
 */

import fetch from 'node-fetch';

const TEST_CONFIG = {
  goal: "Create a comprehensive guide about renewable energy for beginners",
  config: {
    maxIterations: 5, // Lower for testing
    maxRetries: 2,
    timeoutMs: 300000, // 5 minutes for testing
    qualityThreshold: 80,
    enableSelfReflection: true,
    enableQualityGates: true,
    enableRecursionLimits: true,
    verboseLogging: true
  }
};

console.log('🧪 Testing Autonomous Streaming Endpoint');
console.log('==========================================');
console.log(`Goal: ${TEST_CONFIG.goal}`);
console.log(`Config:`, TEST_CONFIG.config);
console.log('');

async function testAutonomousStreaming() {
  try {
    console.log('🚀 Starting autonomous streaming test...');
    
    const response = await fetch('http://localhost:3000/api/autonomous/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In real usage, you'd need proper authentication
        'Cookie': 'next-auth.session-token=test-session' // This is just for testing
      },
      body: JSON.stringify(TEST_CONFIG)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('No response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    console.log('📡 Connected to SSE stream');
    console.log('Listening for events...\n');

    let eventCount = 0;
    let currentEvent = '';
    let buffer = '';
    let finalResult = null;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('event: ')) {
          currentEvent = line.slice(7).trim();
        } else if (line.startsWith('data: ')) {
          buffer = line.slice(6).trim();
        } else if (line === '' && currentEvent && buffer) {
          // End of SSE message
          try {
            if (!buffer) continue;
            
            const data = JSON.parse(buffer);
            eventCount++;
            
            console.log(`📨 Event ${eventCount}: ${currentEvent}`);
            console.log(`   Message: ${data.message || 'No message'}`);
            
            if (data.phase) {
              console.log(`   Phase: ${data.phase}`);
            }
            
            if (data.timestamp) {
              const time = new Date(data.timestamp).toLocaleTimeString();
              console.log(`   Time: ${time}`);
            }
            
            if (currentEvent === 'complete') {
              finalResult = data;
              console.log(`   ✅ Final result received!`);
              if (data.result) {
                console.log(`   Title: ${data.result.title || 'No title'}`);
                console.log(`   Content length: ${data.result.content?.length || 0} chars`);
                console.log(`   Quality score: ${data.qualityScore || 'N/A'}`);
              }
            } else if (currentEvent === 'error') {
              console.log(`   ❌ Error: ${data.error || 'Unknown error'}`);
            } else if (currentEvent === 'completion_trigger') {
              console.log(`   🛑 Completion trigger: ${data.reason || 'Unknown reason'}`);
            }
            
            console.log('');
            
          } catch (parseError) {
            console.warn(`⚠️  Failed to parse SSE data: ${parseError.message}`);
          }
          
          // Reset for next message
          currentEvent = '';
          buffer = '';
        }
      }
    }

    console.log('📊 Test Summary');
    console.log('===============');
    console.log(`Total events received: ${eventCount}`);
    console.log(`Final result: ${finalResult ? '✅ Success' : '❌ No result'}`);
    
    if (finalResult && finalResult.result) {
      console.log(`Content generated: ${finalResult.result.content ? '✅ Yes' : '❌ No'}`);
      console.log(`Quality score: ${finalResult.qualityScore || 'N/A'}`);
      console.log(`Execution time: ${finalResult.executionTime || 'N/A'}ms`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testAutonomousStreaming(); 