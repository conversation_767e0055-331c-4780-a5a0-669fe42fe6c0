#!/usr/bin/env node

/**
 * Test script for Invincible V2 API
 * Tests the server-side execution of the multi-agent system
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

async function testInvincibleV2API() {
  console.log('🧪 Testing Invincible V2 API...\n');
  
  const testRequest = {
    topic: 'AI and machine learning trends 2024',
    customInstructions: 'Focus on practical applications and business impact',
    targetAudience: 'business professionals',
    contentLength: 1500,
    tone: 'professional',
    keywords: ['AI', 'machine learning', 'automation', 'business'],
    contentType: 'article'
  };

  try {
    console.log('📝 Request payload:', JSON.stringify(testRequest, null, 2));
    console.log('\n🚀 Making API request...');
    
    const startTime = Date.now();
    
    const response = await axios.post(`${BASE_URL}/api/invincible-v2`, testRequest, {
      timeout: 180000, // 3 minute timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✅ API request completed in ${duration}s`);
    console.log('\n📊 Response:');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    
    if (response.data.success) {
      console.log('\n🎯 Execution Summary:');
      console.log('- Quality Score:', response.data.result?.qualityScore || 'N/A');
      console.log('- Content Length:', response.data.result?.content?.length || 'N/A');
      console.log('- Research URLs:', response.data.result?.researchUrls?.length || 'N/A');
      console.log('- Execution Time:', response.data.result?.executionTime || 'N/A');
      
      if (response.data.result?.content) {
        console.log('\n📄 Content Preview:');
        console.log(response.data.result.content.substring(0, 300) + '...');
      }
    } else {
      console.log('\n❌ Execution failed:', response.data.error);
    }
    
  } catch (error) {
    console.error('\n❌ API test failed:', error.message);
    
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

// Run the test
testInvincibleV2API(); 