#!/usr/bin/env node

/**
 * Test script to verify that schema markup is not included in article content
 */

import { promises as fs } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));
const rootDir = join(__dirname, '..');

async function testSchemaMarkupFix() {
  console.log('🧪 Testing Schema Markup Fix...\n');
  
  try {
    // Test 1: Check WritingAgent no longer includes schema markup instructions
    console.log('✅ Test 1: Checking WritingAgent schema markup removal...');
    const writingAgentPath = join(rootDir, 'src/lib/agents/v2/writing-agent.ts');
    const writingAgentContent = await fs.readFile(writingAgentPath, 'utf8');
    
    const hasSchemaOpportunities = writingAgentContent.includes('Schema Opportunities:');
    const hasSchemaMarkupInstructions = writingAgentContent.includes('schema markup');
    const hasContentStructureInstead = writingAgentContent.includes('Content Structure:');
    
    console.log(`   - "Schema Opportunities" removed: ${!hasSchemaOpportunities ? '✅' : '❌'}`);
    console.log(`   - Schema markup instructions removed: ${!hasSchemaMarkupInstructions ? '✅' : '❌'}`);
    console.log(`   - "Content Structure" replacement added: ${hasContentStructureInstead ? '✅' : '❌'}`);
    
    // Test 2: Check that content generation focuses on structure, not schema
    console.log('\n✅ Test 2: Checking content generation approach...');
    const hasFaqSectionOptimization = writingAgentContent.includes('FAQ section optimization');
    const hasHowToSectionIntegration = writingAgentContent.includes('How-to section integration');
    const hasArticleFlowImprovement = writingAgentContent.includes('Article flow improvement');
    
    console.log(`   - FAQ section optimization: ${hasFaqSectionOptimization ? '✅' : '❌'}`);
    console.log(`   - How-to section integration: ${hasHowToSectionIntegration ? '✅' : '❌'}`);
    console.log(`   - Article flow improvement: ${hasArticleFlowImprovement ? '✅' : '❌'}`);
    
    // Test 3: Verify schema markup is still available for separate implementation
    console.log('\n✅ Test 3: Checking schema markup availability for separate implementation...');
    
    const enhancedSchemaGeneratorPath = join(rootDir, 'src/lib/agents/enhanced-schema-generator.ts');
    try {
      await fs.access(enhancedSchemaGeneratorPath);
      console.log('   - Enhanced schema generator available: ✅');
    } catch {
      console.log('   - Enhanced schema generator available: ❌');
    }
    
    // Test 4: Check that article patterns still have schema definitions
    console.log('\n✅ Test 4: Checking article patterns schema definitions...');
    const enhancedArticlePatternsPath = join(rootDir, 'src/lib/enhanced-article-patterns-2025.ts');
    
    try {
      const patternsContent = await fs.readFile(enhancedArticlePatternsPath, 'utf8');
      const hasSchemaMarkupDefinitions = patternsContent.includes('schemaMarkup:');
      
      console.log(`   - Schema markup definitions in patterns: ${hasSchemaMarkupDefinitions ? '✅' : '❌'}`);
    } catch {
      console.log('   - Schema markup definitions in patterns: ❌ (file not found)');
    }
    
    console.log('\n🎉 Schema Markup Fix Test Complete!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Schema markup instructions removed from content generation');
    console.log('   ✅ Content generation now focuses on structure and flow');
    console.log('   ✅ Schema markup still available for separate implementation');
    console.log('   ✅ Article patterns retain schema definitions for proper implementation');
    
    console.log('\n💡 Key Changes Made:');
    console.log('   • Removed "Schema Opportunities" from WritingAgent SEO optimization');
    console.log('   • Replaced with "Content Structure" for better content organization');
    console.log('   • Schema markup will no longer appear in article content');
    console.log('   • Schema markup can still be implemented separately via proper channels');
    
    console.log('\n🔧 How Schema Markup Should Be Implemented:');
    console.log('   1. Generate article content (without schema markup)');
    console.log('   2. Use enhanced-schema-generator.ts for proper schema generation');
    console.log('   3. Add schema markup to page head or structured data section');
    console.log('   4. Keep schema markup separate from readable content');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testSchemaMarkupFix().catch(console.error); 