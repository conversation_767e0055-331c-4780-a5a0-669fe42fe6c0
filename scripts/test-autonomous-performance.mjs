#!/usr/bin/env node

/**
 * Performance Test for Optimized Autonomous Agent
 * Tests the speed improvements and functionality after optimization
 */

import { EnhancedAutonomousSupervisor2025 } from '../src/lib/agents/autonomous/EnhancedAutonomousSupervisor2025.js';

async function testAutonomousPerformance() {
  console.log('🚀 Testing Optimized Autonomous Agent Performance');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Quick Test',
      goal: 'Benefits of meditation',
      expectedTime: 60000, // 1 minute target
    },
    {
      name: 'Medium Test', 
      goal: 'Complete guide to sustainable gardening practices',
      expectedTime: 90000, // 1.5 minutes target
    },
    {
      name: 'Complex Test',
      goal: 'Advanced strategies for digital marketing in 2025',
      expectedTime: 120000, // 2 minutes target
    }
  ];

  const results = [];

  for (const testCase of testCases) {
    console.log(`\n🧪 Running ${testCase.name}: "${testCase.goal}"`);
    console.log(`⏱️ Target execution time: ${testCase.expectedTime / 1000}s`);
    
    const startTime = Date.now();
    
    try {
      // Initialize with performance-optimized config
      const supervisor = new EnhancedAutonomousSupervisor2025({
        maxIterations: 6,
        maxRetries: 1,
        timeoutMs: 180000, // 3 minute max
        qualityThreshold: 75,
        enableSelfReflection: false,
        verboseLogging: false
      });

      const result = await supervisor.executeAutonomous(testCase.goal);
      const executionTime = Date.now() - startTime;
      
      const testResult = {
        name: testCase.name,
        goal: testCase.goal,
        success: result.success,
        executionTime,
        targetTime: testCase.expectedTime,
        performance: executionTime <= testCase.expectedTime ? 'PASS' : 'SLOW',
        qualityScore: result.qualityScore || 0,
        wordCount: result.result?.content?.split(' ').length || 0,
        speedImprovement: `${Math.round((testCase.expectedTime - executionTime) / 1000)}s faster than target`
      };

      results.push(testResult);

      console.log(`✅ ${testCase.name} completed in ${executionTime / 1000}s`);
      console.log(`📊 Quality: ${testResult.qualityScore}%`);
      console.log(`📝 Word count: ${testResult.wordCount}`);
      console.log(`🎯 Performance: ${testResult.performance}`);
      
      if (testResult.performance === 'PASS') {
        console.log(`🚀 ${testResult.speedImprovement}`);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      results.push({
        name: testCase.name,
        goal: testCase.goal,
        success: false,
        executionTime,
        targetTime: testCase.expectedTime,
        performance: 'FAILED',
        error: error.message,
        qualityScore: 0,
        wordCount: 0
      });

      console.log(`❌ ${testCase.name} failed: ${error.message}`);
    }
  }

  // Summary Report
  console.log('\n📊 PERFORMANCE OPTIMIZATION SUMMARY');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const passCount = results.filter(r => r.performance === 'PASS').length;
  const avgTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
  const avgQuality = results.reduce((sum, r) => sum + r.qualityScore, 0) / results.length;

  console.log(`✅ Success Rate: ${successCount}/${results.length} (${Math.round(successCount/results.length*100)}%)`);
  console.log(`🚀 Speed Target Met: ${passCount}/${results.length} (${Math.round(passCount/results.length*100)}%)`);
  console.log(`⏱️ Average Execution Time: ${(avgTime / 1000).toFixed(1)}s`);
  console.log(`📊 Average Quality Score: ${avgQuality.toFixed(1)}%`);

  // Detailed Results
  console.log('\n📝 DETAILED RESULTS:');
  results.forEach(result => {
    console.log(`\n${result.name}:`);
    console.log(`  Goal: ${result.goal}`);
    console.log(`  Time: ${(result.executionTime / 1000).toFixed(1)}s / ${(result.targetTime / 1000)}s target`);
    console.log(`  Performance: ${result.performance}`);
    console.log(`  Quality: ${result.qualityScore}%`);
    console.log(`  Words: ${result.wordCount}`);
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    }
  });

  // Performance Recommendations
  console.log('\n💡 OPTIMIZATION STATUS:');
  if (passCount === results.length) {
    console.log('🎉 ALL TESTS PASSED! Autonomous agent is performance optimized.');
  } else if (passCount >= results.length * 0.7) {
    console.log('✅ GOOD PERFORMANCE! Most tests passed speed targets.');
  } else {
    console.log('⚠️ NEEDS OPTIMIZATION! Consider further performance tuning.');
  }

  return {
    successRate: successCount / results.length,
    speedTargetRate: passCount / results.length,
    averageTime: avgTime,
    averageQuality: avgQuality,
    results
  };
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testAutonomousPerformance()
    .then(summary => {
      console.log('\n🏁 Performance test completed successfully!');
      process.exit(summary.successRate === 1 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Performance test failed:', error);
      process.exit(1);
    });
}

export { testAutonomousPerformance }; 