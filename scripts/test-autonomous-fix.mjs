/**
 * Test script for Enhanced Autonomous Supervisor 2025 fixes
 * Tests the research phase to ensure the push error is resolved
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('🔧 Testing Enhanced Autonomous Supervisor 2025 Fixes\n');

async function testAutonomousSystem() {
  try {
    console.log('📡 Testing via API endpoint...');
    
    const testGoal = "How to build a simple garden";
    
    const response = await fetch('http://localhost:3000/api/autonomous', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        goal: testGoal,
        config: {
          maxIterations: 5,
          qualityThreshold: 75,
          verboseLogging: true
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ API Test Failed: ${response.status} - ${errorText}`);
      return;
    }
    
    const result = await response.json();
    
    console.log('✅ API Test Results:');
    console.log(`   - Success: ${result.success}`);
    console.log(`   - Quality Score: ${result.qualityScore || 0}`);
    console.log(`   - Execution Time: ${result.executionTime || 0}ms`);
    
    if (result.error) {
      console.log(`   - Error: ${result.error}`);
    }
    
    if (result.insights) {
      console.log(`   - Total Decisions: ${result.insights.totalDecisions}`);
      console.log(`   - Successful Phases: ${result.insights.successfulPhases}`);
      console.log(`   - Error Count: ${result.insights.errorCount}`);
    }
    
    // Check if the push error is resolved
    if (result.error && result.error.includes("Cannot read properties of undefined (reading 'push')")) {
      console.log('❌ Push error still exists!');
    } else {
      console.log('✅ Push error appears to be resolved!');
    }
    
  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
}

// Wait a moment for the server to start, then run test
setTimeout(testAutonomousSystem, 5000); 