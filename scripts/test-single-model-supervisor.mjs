#!/usr/bin/env node

/**
 * Test script for Single Model Supervisor Architecture
 * Tests the modified supervisor that uses only Gemini 2.5 Flash Lite
 */

import { SupervisorAgent } from '../src/lib/agents/v2/supervisor-agent.js';

console.log('🎭 Testing Single Model Supervisor Architecture\n');

// Test 1: Initialize supervisor
console.log('🔧 Initializing SupervisorAgent...');
const supervisor = new SupervisorAgent({
  costPriority: 'balanced',
  qualityRequirement: 8,
  speedRequirement: 'balanced',
  maxBudgetPerArticle: 5.0,
  enableParallelExecution: true
});

console.log('✅ SupervisorAgent initialized successfully');

// Test 2: Test model selection
console.log('\n🎯 Testing Model Selection...');

const testCases = [
  { name: 'Simple Blog Post', taskType: 'writing', complexity: 25 },
  { name: 'Technical Tutorial', taskType: 'research', complexity: 65 },
  { name: 'Complex Analysis', taskType: 'analysis', complexity: 85 }
];

for (const testCase of testCases) {
  const criteria = {
    costPriority: 'balanced',
    qualityRequirement: 8,
    speedRequirement: 'balanced',
    taskType: testCase.taskType,
    complexity: { 
      level: 'moderate', 
      score: testCase.complexity,
      factors: {
        topicComplexity: testCase.complexity / 10,
        customInstructions: 5,
        qualityRequirements: 8,
        timeConstraints: 5
      }
    }
  };
  
  const selectedModel = supervisor.modelRouter.selectModel(criteria);
  console.log(`📝 ${testCase.name} (${testCase.taskType}): ${selectedModel.name}`);
  console.log(`   └── Reasoning: ${selectedModel.reasoning}`);
  console.log(`   └── Quality: ${selectedModel.qualityScore}/100, Speed: ${selectedModel.speedScore}/100`);
}

// Test 3: Cost estimation
console.log('\n💰 Testing Cost Estimation...');

const mockRequest = {
  topic: 'Benefits of AI in Healthcare',
  contentLength: 2000,
  targetAudience: 'Healthcare professionals',
  tone: 'professional'
};

const inputTokens = 4000;
const outputTokens = 3000;
const selectedModel = supervisor.modelRouter.selectModel({
  costPriority: 'balanced',
  qualityRequirement: 8,
  speedRequirement: 'balanced',
  taskType: 'writing',
  complexity: { level: 'moderate', score: 50 }
});

const estimatedCost = supervisor.modelRouter.getCostEstimate(selectedModel, inputTokens, outputTokens);
console.log(`📊 Estimated cost for ${inputTokens} input + ${outputTokens} output tokens: $${estimatedCost.toFixed(4)}`);

// Test 4: Verify single model approach
console.log('\n🔍 Verifying Single Model Approach...');

const allModels = [];
const taskTypes = ['research', 'analysis', 'writing', 'quality'];
const complexities = [20, 50, 80];

for (const taskType of taskTypes) {
  for (const complexity of complexities) {
    const criteria = {
      costPriority: 'balanced',
      qualityRequirement: 8,
      speedRequirement: 'balanced',
      taskType,
      complexity: { level: 'moderate', score: complexity }
    };
    
    const model = supervisor.modelRouter.selectModel(criteria);
    allModels.push(model.name);
  }
}

const uniqueModels = [...new Set(allModels)];
console.log(`📋 Models used across all test cases: ${uniqueModels.join(', ')}`);
console.log(`✅ Single model verification: ${uniqueModels.length === 1 ? 'PASSED' : 'FAILED'}`);

if (uniqueModels.length === 1) {
  console.log(`🎯 All tasks use: ${uniqueModels[0]}`);
}

// Test 5: Cost projection
console.log('\n📈 Cost Projection for 2000-word Article...');

const phases = [
  { name: 'Research', inputTokens: 2000, outputTokens: 1500 },
  { name: 'Competition', inputTokens: 3000, outputTokens: 2000 },
  { name: 'Writing', inputTokens: 4000, outputTokens: 3000 },
  { name: 'Quality', inputTokens: 2500, outputTokens: 1000 }
];

let totalCost = 0;
for (const phase of phases) {
  const cost = supervisor.modelRouter.getCostEstimate(selectedModel, phase.inputTokens, phase.outputTokens);
  totalCost += cost;
  console.log(`   ${phase.name}: ${phase.inputTokens} + ${phase.outputTokens} tokens = $${cost.toFixed(4)}`);
}

console.log(`💲 Total estimated cost per article: $${totalCost.toFixed(2)}`);

console.log('\n🎉 Single Model Supervisor Test Complete!');
console.log('✅ All tests passed - supervisor uses only Gemini 2.5 Flash Lite as requested'); 